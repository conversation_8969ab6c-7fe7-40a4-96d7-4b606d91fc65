@use 'colors.scss' as *;

#scene-panel {
    top: 102px;
    left: 24px;
    width: 320px;
}

.collapsed #scene-panel {
    display: none;
}

.splat-list-container {
    max-height: 300px;
    overflow: auto;
}

.measure-panel {
    margin-top: 8px;
    padding: 8px;
    background-color: $bcg-primary;
    border-radius: 4px;
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.measure-label {
    font-size: 12px;
    color: $text-primary;
    margin-bottom: 4px;
}

.distance-input {
    width: 100%;
    background-color: $bcg-dark;
    color: $text-primary;
    border: 1px solid $bcg-darker;
    border-radius: 2px;
    padding: 4px;
    font-size: 12px;
}