<<<<<<< HEAD
import { Container, Element, Label } from '@playcanvas/pcui';
=======
import { Container, Element, Label, NumericInput } from 'pcui';
import { Vec3 } from 'playcanvas';
>>>>>>> 41a6b3a (feat: 支持通过改变两点距离调整缩放)

import { Events } from '../events';
import { localize } from './localization';
import { SplatList } from './splat-list';
import sceneImportSvg from './svg/import.svg';
import sceneNewSvg from './svg/new.svg';
import { Tooltips } from './tooltips';
import { Transform } from './transform';

const createSvg = (svgString: string) => {
    const decodedStr = decodeURIComponent(svgString.substring('data:image/svg+xml,'.length));
    return new DOMParser().parseFromString(decodedStr, 'image/svg+xml').documentElement;
};

class ScenePanel extends Container {
    constructor(events: Events, tooltips: Tooltips, args = {}) {
        args = {
            ...args,
            id: 'scene-panel',
            class: 'panel'
        };

        super(args);

        // stop pointer events bubbling
        ['pointerdown', 'pointerup', 'pointermove', 'wheel', 'dblclick'].forEach((eventName) => {
            this.dom.addEventListener(eventName, (event: Event) => event.stopPropagation());
        });

        const sceneHeader = new Container({
            class: 'panel-header'
        });

        const sceneIcon = new Label({
            text: '\uE344',
            class: 'panel-header-icon'
        });

        const sceneLabel = new Label({
            text: localize('scene-manager'),
            class: 'panel-header-label'
        });

        const sceneImport = new Container({
            class: 'panel-header-button'
        });
        sceneImport.dom.appendChild(createSvg(sceneImportSvg));

        const sceneNew = new Container({
            class: 'panel-header-button'
        });
        sceneNew.dom.appendChild(createSvg(sceneNewSvg));

        sceneHeader.append(sceneIcon);
        sceneHeader.append(sceneLabel);
        sceneHeader.append(sceneImport);
        sceneHeader.append(sceneNew);

        sceneImport.on('click', async () => {
            await events.invoke('scene.import');
        });

        sceneNew.on('click', () => {
            events.invoke('doc.new');
        });

        tooltips.register(sceneImport, 'Import Scene', 'top');
        tooltips.register(sceneNew, 'New Scene', 'top');

        const splatList = new SplatList(events);

        const splatListContainer = new Container({
            class: 'splat-list-container'
        });
        splatListContainer.append(splatList);

        const transformHeader = new Container({
            class: 'panel-header'
        });

        const transformIcon = new Label({
            text: '\uE111',
            class: 'panel-header-icon'
        });

        const transformLabel = new Label({
            text: localize('transform'),
            class: 'panel-header-label'
        });

        transformHeader.append(transformIcon);
        transformHeader.append(transformLabel);

        this.append(sceneHeader);
        this.append(splatListContainer);
        this.append(transformHeader);
        this.append(new Transform(events));

        // 距离测量面板
        const measureContainer = new Container({
            id: 'distance-measure-container',
            class: 'measure-panel',
            hidden: true // 默认隐藏，只在测距模式下显示
        });

        const measureLabel = new Label({
            text: localize('measure.distance'),
            class: 'measure-label'
        });

        const distanceInput = new NumericInput({
            class: 'distance-input',
            placeholder: '0.00',
            precision: 2,
            step: 0.01,
            min: 0.01
        });

        measureContainer.append(measureLabel);
        measureContainer.append(distanceInput);

        this.append(measureContainer);

        // 监听测距模式切换事件
        events.on('measure.activate', () => {
            measureContainer.hidden = false;
        });

        events.on('measure.deactivate', () => {
            measureContainer.hidden = true;
            // 不要设置为null，而是保持当前值或设置为0
            // distanceInput.value = null;
        });

        // 存储当前测距的原始距离，用于缩放计算
        let originalDistance = 0;
        let isUpdatingFromScale = false; // 防止循环更新的标志

        // 监听测距开始事件，记录原始距离
        events.on('measure.distanceSet', (distance: number) => {
            originalDistance = distance;
            isUpdatingFromScale = true;
            distanceInput.value = distance;
            isUpdatingFromScale = false;
        });

        // 监听缩放变化事件，实时更新测距长度
        events.on('pivot.moved', (pivot: any) => {
            if (originalDistance > 0 && !measureContainer.hidden && !isUpdatingFromScale) {
                // 获取当前缩放值
                const currentScale = pivot.transform.scale.x;
                // 计算新的距离
                const newDistance = originalDistance * currentScale;
                // 更新显示（防止循环更新）
                isUpdatingFromScale = true;
                distanceInput.value = newDistance;
                isUpdatingFromScale = false;
                // 通知测距工具更新视觉反馈
                events.fire('measure.scaleChanged', currentScale);
            }
        });

        // 监听距离输入变化事件，反向更新缩放
        distanceInput.on('change', (newDistance: number) => {
            if (!isUpdatingFromScale && originalDistance > 0 && newDistance > 0 && !measureContainer.hidden) {
                // 计算需要的缩放值
                const targetScale = newDistance / originalDistance;

                // 获取当前的pivot并更新缩放
                const pivot = events.invoke('pivot');
                if (pivot) {
                    // 开始变换
                    pivot.start();

                    // 获取当前变换
                    const currentTransform = pivot.transform;
                    const newPosition = currentTransform.position.clone();
                    const newRotation = currentTransform.rotation.clone();
                    const newScale = new Vec3(targetScale, targetScale, targetScale);

                    // 应用新的变换
                    pivot.moveTRS(newPosition, newRotation, newScale);

                    // 结束变换
                    pivot.end();

                    // 通知测距工具更新视觉反馈
                    events.fire('measure.scaleChanged', targetScale);
                }
            }
        });

        this.append(new Element({
            class: 'panel-header',
            height: 20
        }));
    }
}

export { ScenePanel };
