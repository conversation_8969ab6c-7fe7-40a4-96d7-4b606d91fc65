import { Entity, Vec3 } from 'playcanvas';
import * as pc from 'playcanvas';

import { Events } from '../events';
import { Scene } from '../scene';
import { Splat } from '../splat';

export class MeasureTool {
    events: Events;
    scene: Scene;
    parent: HTMLElement;
    active = false;
    startPoint: Vec3 = null;
    endPoint: Vec3 = null;
    startSplat: Splat = null;
    endSplat: Splat = null;
    lineEntity: Entity = null;
    startPointMarker: Entity = null;
    endPointMarker: Entity = null;
    currentDistance: number = 0;

    constructor(events: Events, scene: Scene, parent: HTMLElement) {
        this.events = events;
        this.scene = scene;
        this.parent = parent;

        // 鼠标点击处理
        const pointerdown = (e: PointerEvent) => {
            if (e.button !== 0 || e.altKey) return; // 只处理左键

            e.preventDefault();
            e.stopPropagation();

            // 使用专门的splat点选择方法，不移动相机
            this.pickSplatPointForMeasurement(e.offsetX, e.offsetY);
        };

        // 键盘事件处理
        const keydown = (e: KeyboardEvent) => {
            if (e.key === 'Backspace') {
                this.clearMeasurement();
            } else if (e.key === 'Escape') {
                events.fire('tool.deactivate');
            }
        };

        this.activate = () => {
            this.parent.style.display = 'block';
            this.parent.addEventListener('pointerdown', pointerdown);
            document.addEventListener('keydown', keydown);
            this.events.fire('measure.activate');
        };

        this.deactivate = () => {
            this.parent.style.display = 'none';
            this.parent.removeEventListener('pointerdown', pointerdown);
            document.removeEventListener('keydown', keydown);
            this.clearMeasurement();
            this.scene.forceRender = true;
            this.events.fire('measure.deactivate');
        };

        // 监听缩放变化事件，实时更新测量线的视觉反馈
        events.on('measure.scaleChanged', (scale: number) => {
            this.updateMeasureLineForScale(scale);
        });
    }

    activate: () => void;
    deactivate: () => void;

    // 专门用于测距的splat点选择方法，不移动相机
    pickSplatPointForMeasurement(screenX: number, screenY: number) {
        const camera = this.scene.camera;
        if (!camera) return;

        // 拦截相机移动
        const originalSetFocalPoint = camera.setFocalPoint;
        const originalSetDistance = camera.setDistance;

        let pickedSplat = null;
        let pickedPosition = null;

        // 临时禁用相机移动
        camera.setFocalPoint = () => {};
        camera.setDistance = () => {};

        // 监听pick事件
        const handlePick = (details: { splat: Splat, position: Vec3 }) => {
            pickedSplat = details.splat;
            pickedPosition = details.position.clone();
        };

        this.scene.events.once('camera.focalPointPicked', handlePick);

        // 执行pick
        camera.pickFocalPoint(screenX, screenY);

        // 恢复相机方法
        camera.setFocalPoint = originalSetFocalPoint;
        camera.setDistance = originalSetDistance;

        // 处理结果
        if (pickedSplat && pickedPosition) {
            this.handleSplatPointPicked(pickedSplat, pickedPosition);
        }
    }

    // 处理splat点选择
    handleSplatPointPicked(splat: Splat, position: Vec3) {
        if (!this.startPoint) {
            // 选择起始点
            this.startPoint = position.clone();
            this.startSplat = splat;
            this.createPointMarker(position, 'start');
            this.events.fire('measure.startPoint', this.startPoint);
        } else if (!this.endPoint) {
            // 选择结束点
            this.endPoint = position.clone();
            this.endSplat = splat;
            this.createPointMarker(position, 'end');
            this.events.fire('measure.endPoint', this.endPoint);

            // 创建测量线
            this.createMeasureLine();

            // 显示距离
            const distance = this.calculateDistance(this.startPoint, this.endPoint);
            this.updateDistanceDisplay(distance);
        } else {
            // 如果已经有两个点，开始新的测距
            this.clearMeasurement();
            this.startPoint = position.clone();
            this.startSplat = splat;
            this.createPointMarker(position, 'start');
            this.events.fire('measure.startPoint', this.startPoint);
        }
    }

    calculateDistance(point1: Vec3, point2: Vec3): number {
        return Math.sqrt(
            Math.pow(point2.x - point1.x, 2) +
            Math.pow(point2.y - point1.y, 2) +
            Math.pow(point2.z - point1.z, 2)
        );
    }

    // 获取当前的缩放值
    getCurrentScale(): number {
        try {
            const pivot = this.events.invoke('pivot');
            return pivot?.transform?.scale?.x || 1;
        } catch (error) {
            return 1; // 默认缩放值
        }
    }

    updateDistanceDisplay(distance: number) {
        this.currentDistance = distance;
        // 发送距离设置事件，让scene-panel记录原始距离
        this.events.fire('measure.distanceSet', distance);
    }

    clearMeasurement() {
        this.startPoint = null;
        this.endPoint = null;
        this.startSplat = null;
        this.endSplat = null;
        this.currentDistance = 0;

        if (this.lineEntity) {
            this.lineEntity.destroy();
            this.lineEntity = null;
        }

        if (this.startPointMarker) {
            this.startPointMarker.destroy();
            this.startPointMarker = null;
        }

        if (this.endPointMarker) {
            this.endPointMarker.destroy();
            this.endPointMarker = null;
        }
    }

    // 创建点标记
    createPointMarker(position: Vec3, type: 'start' | 'end') {
        if (!this.scene?.app) return;

        const marker = new Entity(`measurePoint_${type}`);

        const material = new pc.StandardMaterial();
        if (type === 'start') {
            material.diffuse.set(0, 1, 0);
            material.emissive.set(0, 0.5, 0);
        } else {
            material.diffuse.set(1, 0, 0);
            material.emissive.set(0.5, 0, 0);
        }
        material.update();

        marker.addComponent('render', {
            type: 'sphere',
            material: material
        });

        marker.setPosition(position);

        // 根据当前缩放值动态调节点标记大小
        const currentScale = this.getCurrentScale();
        const markerSize = 0.1 * Math.pow(currentScale, 0.3);
        marker.setLocalScale(markerSize, markerSize, markerSize);

        this.scene.app.root.addChild(marker);

        if (type === 'start') {
            this.startPointMarker = marker;
        } else {
            this.endPointMarker = marker;
        }

        this.scene.forceRender = true;
    }

    // 创建测量线
    createMeasureLine() {
        if (this.lineEntity) {
            this.lineEntity.destroy();
        }

        if (!this.scene?.app) return;

        this.lineEntity = new Entity('measureLine');

        const material = new pc.StandardMaterial();
        material.diffuse.set(1, 1, 0);
        material.emissive.set(1, 1, 0);
        material.update();

        this.lineEntity.addComponent('render', {
            type: 'cylinder',
            material: material
        });

        const midPoint = new Vec3().add2(this.startPoint, this.endPoint).mulScalar(0.5);
        const distance = this.calculateDistance(this.startPoint, this.endPoint);
        const direction = new Vec3().sub2(this.endPoint, this.startPoint).normalize();

        this.lineEntity.setPosition(midPoint);

        const up = new Vec3(0, 1, 0);
        const dot = direction.dot(up);

        if (Math.abs(dot) > 0.99) {
            const right = new Vec3(1, 0, 0);
            this.lineEntity.lookAt(direction.x > 0 ? right : new Vec3(-1, 0, 0));
        } else {
            this.lineEntity.lookAt(this.endPoint);
        }

        this.lineEntity.rotateLocal(90, 0, 0);

        // 根据当前缩放值动态调节线条粗细
        const currentScale = this.getCurrentScale();
        const lineThickness = 0.02 * Math.pow(currentScale, 0.3);
        this.lineEntity.setLocalScale(lineThickness, distance, lineThickness);

        this.scene.app.root.addChild(this.lineEntity);
        this.scene.forceRender = true;
    }

    // 更新测量线位置以反映缩放变化
    updateMeasureLineForScale(scale: number) {
        if (this.startPoint && this.endPoint && this.lineEntity) {
            // 计算缩放后的点位置（不修改原始点）
            const scaledStartPoint = this.startPoint.clone().mulScalar(scale);
            const scaledEndPoint = this.endPoint.clone().mulScalar(scale);

            // 重新计算线条属性
            const midPoint = new Vec3().add2(scaledStartPoint, scaledEndPoint).mulScalar(0.5);
            const distance = this.calculateDistance(scaledStartPoint, scaledEndPoint);
            const direction = new Vec3().sub2(scaledEndPoint, scaledStartPoint).normalize();

            // 更新线条位置
            this.lineEntity.setPosition(midPoint);

            // 更新线条旋转
            const up = new Vec3(0, 1, 0);
            const dot = direction.dot(up);

            if (Math.abs(dot) > 0.99) {
                const right = new Vec3(1, 0, 0);
                this.lineEntity.lookAt(direction.x > 0 ? right : new Vec3(-1, 0, 0));
            } else {
                this.lineEntity.lookAt(scaledEndPoint);
            }

            this.lineEntity.rotateLocal(90, 0, 0);

            // 动态调节线条粗细：基础粗细 * 缩放因子
            // 使用对数缩放来避免线条过粗或过细
            const lineThickness = 0.02 * Math.pow(scale, 0.3);
            this.lineEntity.setLocalScale(lineThickness, distance, lineThickness);

            // 更新点标记位置和大小
            if (this.startPointMarker) {
                this.startPointMarker.setPosition(scaledStartPoint);
                // 动态调节点标记大小：基础大小 * 缩放因子
                const markerSize = 0.1 * Math.pow(scale, 0.3);
                this.startPointMarker.setLocalScale(markerSize, markerSize, markerSize);
            }
            if (this.endPointMarker) {
                this.endPointMarker.setPosition(scaledEndPoint);
                // 动态调节点标记大小：基础大小 * 缩放因子
                const markerSize = 0.1 * Math.pow(scale, 0.3);
                this.endPointMarker.setLocalScale(markerSize, markerSize, markerSize);
            }

            this.scene.forceRender = true;
        }
    }
}
