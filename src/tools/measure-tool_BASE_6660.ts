import { Events } from '../events';
import { Scene } from '../scene';

class MeasureTool {
    events: Events;
    scene: Scene;
    active = false;

    constructor(events: Events, scene: Scene) {
        this.events = events;
        this.scene = scene;
    }

    activate() {
        this.active = true;
        this.events.fire('measure.activate');
    }

    deactivate() {
        this.active = false;
        this.events.fire('measure.deactivate');
    }
}

export { MeasureTool };
