<!DOCTYPE html>
<html>
<head>
    <title>Speed Test Console</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        #output { 
            background: #f0f0f0; 
            padding: 20px; 
            font-family: monospace; 
            white-space: pre-wrap;
            border: 1px solid #ccc;
            max-height: 500px;
            overflow-y: auto;
        }
        button { 
            padding: 10px 20px; 
            font-size: 16px; 
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>Speed Uniformity Test</h1>
    <p>This test verifies that our smoothness fix provides uniform speed when smoothness=1.</p>
    <button onclick="runTest()">Run Speed Test</button>
    <div id="output"></div>

    <script src="test-speed-analysis.js"></script>
    <script>
        function runTest() {
            const output = document.getElementById('output');
            
            // Capture console.log output
            const originalLog = console.log;
            let logOutput = '';
            console.log = function(...args) {
                logOutput += args.join(' ') + '\n';
                originalLog.apply(console, arguments);
            };
            
            // Run the test
            testSpeedUniformity();
            
            // Restore console.log and display output
            console.log = originalLog;
            output.textContent = logOutput;
        }
    </script>
</body>
</html>
