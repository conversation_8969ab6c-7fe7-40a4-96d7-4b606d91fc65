// Test script to analyze speed behavior with our smoothness fix
// This can be run in Node.js or browser console

class CubicSpline {
    constructor(times, knots) {
        this.times = times;
        this.knots = knots;
        this.dim = knots.length / times.length / 3;
    }

    evaluate(time, result) {
        const { times } = this;
        const last = times.length - 1;

        if (time <= times[0]) {
            this.getKnot(0, result);
        } else if (time >= times[last]) {
            this.getKnot(last, result);
        } else {
            let seg = 0;
            while (time >= times[seg + 1]) {
                seg++;
            }
            return this.evaluateSegment(seg, (time - times[seg]) / (times[seg + 1] - times[seg]), result);
        }
    }

    getKnot(index, result) {
        const { knots, dim } = this;
        const idx = index * dim * 3;
        for (let i = 0; i < dim; ++i) {
            result[i] = knots[idx + i * 3 + 1];
        }
    }

    evaluateSegment(segment, t, result) {
        const { knots, dim } = this;

        const t2 = t * t;
        const twot = t + t;
        const omt = 1 - t;
        const omt2 = omt * omt;

        let idx = segment * dim * 3;
        for (let i = 0; i < dim; ++i) {
            const p0 = knots[idx + 1];
            const m0 = knots[idx + 2];
            const m1 = knots[idx + dim * 3];
            const p1 = knots[idx + dim * 3 + 1];
            idx += 3;

            result[i] =
                p0 * ((1 + twot) * omt2) +
                m0 * (t * omt2) +
                p1 * (t2 * (3 - twot)) +
                m1 * (t2 * (t - 1));
        }
    }

    static fromPoints(times, points, tension = 0) {
        const dim = points.length / times.length;
        const knots = new Array(times.length * dim * 3);

        for (let i = 0; i < times.length; i++) {
            const t = times[i];

            for (let j = 0; j < dim; j++) {
                const idx = i * dim + j;
                const p = points[idx];

                let tangent;

                if (i === 0) {
                    tangent = (points[idx + dim] - p) / (times[i + 1] - t);
                } else if (i === times.length - 1) {
                    tangent = (p - points[idx - dim]) / (t - times[i - 1]);
                } else {
                    if (tension >= 0.99) {
                        tangent = 0;
                    } else {
                        const dt1 = times[i] - times[i - 1];
                        const dt2 = times[i + 1] - times[i];

                        // Our fix: when smoothness is 1 (tension is 0), we want uniform speed
                        if (tension <= 0.01) {
                            const totalDt = dt1 + dt2;
                            const totalDp = points[idx + dim] - points[idx - dim];
                            tangent = totalDp / totalDt;
                        } else {
                            const slope1 = (p - points[idx - dim]) / dt1;
                            const slope2 = (points[idx + dim] - p) / dt2;
                            
                            const catmullRomTangent = 0.5 * (slope1 + slope2);
                            
                            const totalDt = dt1 + dt2;
                            const totalDp = points[idx + dim] - points[idx - dim];
                            const uniformTangent = totalDp / totalDt;
                            
                            const blendFactor = tension;
                            tangent = (1 - blendFactor) * uniformTangent + blendFactor * catmullRomTangent;
                        }
                    }
                }

                // Our fix: don't scale down tangent for very low tension
                if (tension <= 0.01) {
                    // Keep the tangent as calculated for uniform speed
                } else {
                    tangent *= (1.0 - tension);
                }

                knots[idx * 3] = tangent;
                knots[idx * 3 + 1] = p;
                knots[idx * 3 + 2] = tangent;
            }
        }

        return new CubicSpline(times, knots);
    }
}

function calculateSpeed(spline, time, dt = 0.1) {
    const result1 = [];
    const result2 = [];
    spline.evaluate(time - dt/2, result1);
    spline.evaluate(time + dt/2, result2);
    
    const dx = result2[0] - result1[0];
    const dy = result2[1] - result1[1];
    return Math.sqrt(dx*dx + dy*dy) / dt;
}

function testSpeedUniformity() {
    // Test data: keyframes at different times
    const times = [0, 30, 60, 90, 120];
    const points = [100, 50, 300, 150, 500, 50, 200, 150, 400, 100]; // x,y pairs

    console.log('=== Speed Uniformity Test ===');
    console.log('Testing smoothness values: 0, 0.5, 1.0');
    console.log('Expected: smoothness=1 should have uniform speed (low standard deviation)');
    console.log('');

    [0, 0.5, 1.0].forEach(smoothness => {
        const tension = 1.0 - smoothness;
        const spline = CubicSpline.fromPoints(times, points, tension);
        
        console.log(`--- Smoothness: ${smoothness} (tension: ${tension}) ---`);
        
        const speeds = [];
        for (let i = 0; i < times.length; i++) {
            const speed = calculateSpeed(spline, times[i]);
            speeds.push(speed);
            console.log(`Frame ${times[i]}: Speed = ${speed.toFixed(2)} units/frame`);
        }
        
        const avgSpeed = speeds.reduce((a, b) => a + b, 0) / speeds.length;
        const speedVariance = speeds.reduce((sum, speed) => sum + Math.pow(speed - avgSpeed, 2), 0) / speeds.length;
        const speedStdDev = Math.sqrt(speedVariance);
        const uniformityRatio = speedStdDev / avgSpeed;
        
        console.log(`Average Speed: ${avgSpeed.toFixed(2)}`);
        console.log(`Speed Standard Deviation: ${speedStdDev.toFixed(2)}`);
        console.log(`Uniformity Ratio: ${uniformityRatio.toFixed(3)} (lower is better)`);
        console.log(`Speed Uniformity: ${uniformityRatio < 0.1 ? 'EXCELLENT' : uniformityRatio < 0.3 ? 'GOOD' : 'POOR'}`);
        console.log('');
    });
}

// Run the test
if (typeof module !== 'undefined' && module.exports) {
    // Node.js environment
    module.exports = { CubicSpline, testSpeedUniformity };
    testSpeedUniformity();
} else {
    // Browser environment
    window.testSpeedUniformity = testSpeedUniformity;
    testSpeedUniformity();
}
