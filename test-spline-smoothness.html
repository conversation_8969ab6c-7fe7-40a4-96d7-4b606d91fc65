<!DOCTYPE html>
<html>
<head>
    <title>Spline Smoothness Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 10px; border: 1px solid #ccc; }
        .controls { margin: 10px 0; }
        canvas { border: 1px solid #000; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>Spline Smoothness Test</h1>

    <div class="test-section">
        <h2>Test 1: Spline Evaluation at Keyframes</h2>
        <div class="controls">
            <label>Smoothness: <input type="range" id="smoothness1" min="0" max="1" step="0.1" value="0"></label>
            <span id="smoothness1-value">0</span>
        </div>
        <canvas id="canvas1" width="600" height="200"></canvas>
        <div id="results1"></div>
    </div>

    <div class="test-section">
        <h2>Test 2: Animation Timing</h2>
        <div class="controls">
            <label>Smoothness: <input type="range" id="smoothness2" min="0" max="1" step="0.1" value="0"></label>
            <span id="smoothness2-value">0</span>
            <button id="animate">Animate</button>
            <button id="stop">Stop</button>
        </div>
        <canvas id="canvas2" width="600" height="200"></canvas>
        <div id="results2"></div>
    </div>

    <script>
        // Simple CubicSpline implementation for testing
class CubicSpline {
    // control times
    times;

    // control data: in-tangent, point, out-tangent
    knots;

    // dimension of the knot points
    dim;

    constructor(times, knots) {
        this.times = times;
        this.knots = knots;
        this.dim = knots.length / times.length / 3;
    }

    evaluate(time, result) {
        const { times } = this;
        const last = times.length - 1;

        if (time <= times[0]) {
            this.getKnot(0, result);
        } else if (time >= times[last]) {
            this.getKnot(last, result);
        } else {
            let seg = 0;
            while (time >= times[seg + 1]) {
                seg++;
            }
            return this.evaluateSegment(seg, (time - times[seg]) / (times[seg + 1] - times[seg]), result);
        }
    }

    getKnot(index, result) {
        const { knots, dim } = this;
        const idx = index * 3 * dim;
        for (let i = 0; i < dim; ++i) {
            result[i] = knots[idx + i * 3 + 1];
        }
    }

    // evaluate the spline segment at the given normalized time t
    evaluateSegment(segment, t, result) {
        const { knots, dim } = this;

        const t2 = t * t;
        const twot = t + t;
        const omt = 1 - t;
        const omt2 = omt * omt;

        let idx = segment * dim * 3;                    // each knot has 3 values: tangent in, value, tangent out
        for (let i = 0; i < dim; ++i) {
            const p0 = knots[idx + 1];                  // p0
            const m0 = knots[idx + 2];                  // outgoing tangent
            const m1 = knots[idx + dim * 3];            // incoming tangent
            const p1 = knots[idx + dim * 3 + 1];        // p1
            idx += 3;

            result[i] =
                p0 * ((1 + twot) * omt2) +
                m0 * (t * omt2) +
                p1 * (t2 * (3 - twot)) +
                m1 * (t2 * (t - 1));
        }
    }

    // create cubic spline data from a set of control points to be interpolated
    // times: time values for each control point
    // points: control point values to be interpolated (n dimensional)
    // tension: level of smoothness, 0 = smooth, 1 = linear interpolation
    static fromPoints(times, points, tension = 0) {
        const dim = points.length / times.length;
        const knots = new Array(times.length * dim * 3);

        for (let i = 0; i < times.length; i++) {
            const t = times[i];

            for (let j = 0; j < dim; j++) {
                const idx = i * dim + j;
                const p = points[idx];

                let tangent;

                if (i === 0) {
                    // First point: use forward difference
                    tangent = (points[idx + dim] - p) / (times[i + 1] - t);
                } else if (i === times.length - 1) {
                    // Last point: use backward difference
                    tangent = (p - points[idx - dim]) / (t - times[i - 1]);
                } else {
                    // Middle points: different calculation based on tension
                    if (tension >= 0.99) {
                        // For high tension (linear), use zero tangents for linear interpolation
                        tangent = 0;
                    } else {
                        // For smooth curves, use time-normalized Catmull-Rom tangents
                        const dt1 = times[i] - times[i - 1];
                        const dt2 = times[i + 1] - times[i];

                        // When smoothness is 1 (tension is 0), we want uniform speed
                        // Calculate tangent that maintains constant velocity
                        if (tension <= 0.01) {
                            // For uniform speed, tangent should be proportional to the average velocity
                            const totalDt = dt1 + dt2;
                            const totalDp = points[idx + dim] - points[idx - dim];
                            tangent = totalDp / totalDt;
                        } else {
                            // For partial smoothness, blend between uniform speed and Catmull-Rom
                            const slope1 = (p - points[idx - dim]) / dt1;
                            const slope2 = (points[idx + dim] - p) / dt2;

                            // Catmull-Rom tangent
                            const catmullRomTangent = 0.5 * (slope1 + slope2);

                            // Uniform speed tangent
                            const totalDt = dt1 + dt2;
                            const totalDp = points[idx + dim] - points[idx - dim];
                            const uniformTangent = totalDp / totalDt;

                            // Blend based on tension (lower tension = more uniform speed)
                            const blendFactor = tension;
                            tangent = (1 - blendFactor) * uniformTangent + blendFactor * catmullRomTangent;
                        }
                    }
                }

                // For very low tension (high smoothness), don't scale down the tangent
                // This ensures we maintain the calculated velocity for uniform motion
                if (tension <= 0.01) {
                    // Keep the tangent as calculated for uniform speed
                } else {
                    // Apply tension scaling for other cases
                    tangent *= (1.0 - tension);
                }

                knots[idx * 3] = tangent;     // in-tangent
                knots[idx * 3 + 1] = p;      // point value
                knots[idx * 3 + 2] = tangent; // out-tangent
            }
        }

        return new CubicSpline(times, knots);
    }


    // create a looping spline by duplicating animation points at the end and beginning
    static fromPointsLooping(length, times, points, tension = 0) {
        if (times.length < 2) {
            return CubicSpline.fromPoints(times, points, tension);
        }

        const dim = points.length / times.length;
        const newTimes = times.slice();
        const newPoints = points.slice();

        // append first two points
        newTimes.push(length + times[0], length + times[1]);
        newPoints.push(...points.slice(0, dim * 2));

        // prepend last two points
        newTimes.splice(0, 0, times[times.length - 2] - length, times[times.length - 1] - length);
        newPoints.splice(0, 0, ...points.slice(points.length - dim * 2));

        return CubicSpline.fromPoints(newTimes, newPoints, tension);
    }
}
        // Test data
        const times = [0, 30, 60, 90];
        const points = [100, 50, 300, 150, 500, 50, 200, 150]; // x,y pairs

        function drawSpline(canvasId, smoothness) {
            const canvas = document.getElementById(canvasId);
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            const tension = 1.0 - smoothness;
            const spline = CubicSpline.fromPoints(times, points, tension);

            // Draw keyframes
            ctx.fillStyle = 'red';
            for (let i = 0; i < times.length; i++) {
                const x = points[i * 2];
                const y = points[i * 2 + 1];
                ctx.fillRect(x - 3, y - 3, 6, 6);
                ctx.fillText(`Frame ${times[i]}`, x + 5, y - 5);
            }

            // Draw spline curve
            ctx.strokeStyle = smoothness < 0.5 ? 'orange' : 'blue';
            ctx.lineWidth = 2;
            ctx.beginPath();

            const result = [];
            for (let t = times[0]; t <= times[times.length - 1]; t += 0.5) {
                spline.evaluate(t, result);
                if (t === times[0]) {
                    ctx.moveTo(result[0], result[1]);
                } else {
                    ctx.lineTo(result[0], result[1]);
                }
            }
            ctx.stroke();

            return spline;
        }

        function testKeyframeEvaluation(spline, resultsId) {
            const resultsDiv = document.getElementById(resultsId);
            let html = '<h3>Evaluation at keyframes:</h3>';

            const result = [];
            times.forEach((time, i) => {
                spline.evaluate(time, result);
                const expectedX = points[i * 2];
                const expectedY = points[i * 2 + 1];
                const diffX = Math.abs(result[0] - expectedX);
                const diffY = Math.abs(result[1] - expectedY);

                html += `<p>Frame ${time}: Expected (${expectedX}, ${expectedY}), Got (${result[0].toFixed(2)}, ${result[1].toFixed(2)}), Diff: (${diffX.toFixed(2)}, ${diffY.toFixed(2)})</p>`;
            });

            resultsDiv.innerHTML = html;
        }

        // Setup controls
        const smoothness1 = document.getElementById('smoothness1');
        const smoothness1Value = document.getElementById('smoothness1-value');

        smoothness1.addEventListener('input', () => {
            const value = parseFloat(smoothness1.value);
            smoothness1Value.textContent = value;
            const spline = drawSpline('canvas1', value);
            testKeyframeEvaluation(spline, 'results1');
        });

        const smoothness2 = document.getElementById('smoothness2');
        const smoothness2Value = document.getElementById('smoothness2-value');

        smoothness2.addEventListener('input', () => {
            const value = parseFloat(smoothness2.value);
            smoothness2Value.textContent = value;
            drawSpline('canvas2', value);
        });

        // Animation test
        let animationId = null;
        let currentTime = 0;

        document.getElementById('animate').addEventListener('click', () => {
            if (animationId) return;

            const canvas = document.getElementById('canvas2');
            const ctx = canvas.getContext('2d');
            const smoothness = parseFloat(smoothness2.value);
            const tension = 1.0 - smoothness;
            const spline = CubicSpline.fromPoints(times, points, tension);

            function animate() {
                drawSpline('canvas2', smoothness);

                // Draw current position
                const result = [];
                spline.evaluate(currentTime, result);
                ctx.fillStyle = 'green';
                ctx.fillRect(result[0] - 5, result[1] - 5, 10, 10);

                // Update time
                currentTime += 0.5;
                if (currentTime > times[times.length - 1]) {
                    currentTime = times[0];
                }

                animationId = requestAnimationFrame(animate);
            }

            animate();
        });

        document.getElementById('stop').addEventListener('click', () => {
            if (animationId) {
                cancelAnimationFrame(animationId);
                animationId = null;
            }
        });

        // Initial draw
        const initialSpline = drawSpline('canvas1', 0);
        testKeyframeEvaluation(initialSpline, 'results1');
        drawSpline('canvas2', 0);
    </script>
</body>
</html>
