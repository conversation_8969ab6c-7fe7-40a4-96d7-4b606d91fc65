<!DOCTYPE html>
<html>
<head>
    <title>Smoothness Fix Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 10px; border: 1px solid #ccc; }
        .controls { margin: 10px 0; }
        canvas { border: 1px solid #000; margin: 10px 0; }
        .results { margin: 10px 0; font-family: monospace; }
    </style>
</head>
<body>
    <h1>Smoothness Fix Test</h1>
    
    <div class="test-section">
        <h2>Speed Analysis at Keyframes</h2>
        <div class="controls">
            <label>Smoothness: <input type="range" id="smoothness" min="0" max="1" step="0.1" value="1"></label>
            <span id="smoothness-value">1</span>
        </div>
        <canvas id="canvas" width="800" height="300"></canvas>
        <div id="speed-results" class="results"></div>
    </div>

    <script type="module">
        // Import our fixed spline implementation
        class CubicSpline {
            constructor(times, knots) {
                this.times = times;
                this.knots = knots;
                this.dim = knots.length / times.length / 3;
            }

            evaluate(time, result) {
                const { times } = this;
                const last = times.length - 1;

                if (time <= times[0]) {
                    this.getKnot(0, result);
                } else if (time >= times[last]) {
                    this.getKnot(last, result);
                } else {
                    let seg = 0;
                    while (time >= times[seg + 1]) {
                        seg++;
                    }
                    return this.evaluateSegment(seg, (time - times[seg]) / (times[seg + 1] - times[seg]), result);
                }
            }

            getKnot(index, result) {
                const { knots, dim } = this;
                const idx = index * dim * 3;
                for (let i = 0; i < dim; ++i) {
                    result[i] = knots[idx + i * 3 + 1];
                }
            }

            evaluateSegment(segment, t, result) {
                const { knots, dim } = this;

                const t2 = t * t;
                const twot = t + t;
                const omt = 1 - t;
                const omt2 = omt * omt;

                let idx = segment * dim * 3;
                for (let i = 0; i < dim; ++i) {
                    const p0 = knots[idx + 1];
                    const m0 = knots[idx + 2];
                    const m1 = knots[idx + dim * 3];
                    const p1 = knots[idx + dim * 3 + 1];
                    idx += 3;

                    result[i] =
                        p0 * ((1 + twot) * omt2) +
                        m0 * (t * omt2) +
                        p1 * (t2 * (3 - twot)) +
                        m1 * (t2 * (t - 1));
                }
            }

            static fromPoints(times, points, tension = 0) {
                const dim = points.length / times.length;
                const knots = new Array(times.length * dim * 3);

                for (let i = 0; i < times.length; i++) {
                    const t = times[i];

                    for (let j = 0; j < dim; j++) {
                        const idx = i * dim + j;
                        const p = points[idx];

                        let tangent;

                        if (i === 0) {
                            tangent = (points[idx + dim] - p) / (times[i + 1] - t);
                        } else if (i === times.length - 1) {
                            tangent = (p - points[idx - dim]) / (t - times[i - 1]);
                        } else {
                            if (tension >= 0.99) {
                                tangent = 0;
                            } else {
                                const dt1 = times[i] - times[i - 1];
                                const dt2 = times[i + 1] - times[i];

                                // Our fix: when smoothness is 1 (tension is 0), we want uniform speed
                                if (tension <= 0.01) {
                                    const totalDt = dt1 + dt2;
                                    const totalDp = points[idx + dim] - points[idx - dim];
                                    tangent = totalDp / totalDt;
                                } else {
                                    const slope1 = (p - points[idx - dim]) / dt1;
                                    const slope2 = (points[idx + dim] - p) / dt2;
                                    
                                    const catmullRomTangent = 0.5 * (slope1 + slope2);
                                    
                                    const totalDt = dt1 + dt2;
                                    const totalDp = points[idx + dim] - points[idx - dim];
                                    const uniformTangent = totalDp / totalDt;
                                    
                                    const blendFactor = tension;
                                    tangent = (1 - blendFactor) * uniformTangent + blendFactor * catmullRomTangent;
                                }
                            }
                        }

                        // Our fix: don't scale down tangent for very low tension
                        if (tension <= 0.01) {
                            // Keep the tangent as calculated for uniform speed
                        } else {
                            tangent *= (1.0 - tension);
                        }

                        knots[idx * 3] = tangent;
                        knots[idx * 3 + 1] = p;
                        knots[idx * 3 + 2] = tangent;
                    }
                }

                return new CubicSpline(times, knots);
            }
        }

        // Test data: keyframes at different times
        const times = [0, 30, 60, 90, 120];
        const points = [100, 50, 300, 150, 500, 50, 200, 150, 400, 100]; // x,y pairs

        function calculateSpeed(spline, time, dt = 0.1) {
            const result1 = [];
            const result2 = [];
            spline.evaluate(time - dt/2, result1);
            spline.evaluate(time + dt/2, result2);
            
            const dx = result2[0] - result1[0];
            const dy = result2[1] - result1[1];
            return Math.sqrt(dx*dx + dy*dy) / dt;
        }

        function testSmoothness(smoothness) {
            const tension = 1.0 - smoothness;
            const spline = CubicSpline.fromPoints(times, points, tension);
            
            // Draw the spline
            const canvas = document.getElementById('canvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // Draw path
            ctx.strokeStyle = 'blue';
            ctx.lineWidth = 2;
            ctx.beginPath();
            const result = [];
            for (let t = times[0]; t <= times[times.length - 1]; t += 0.5) {
                spline.evaluate(t, result);
                if (t === times[0]) {
                    ctx.moveTo(result[0], result[1]);
                } else {
                    ctx.lineTo(result[0], result[1]);
                }
            }
            ctx.stroke();
            
            // Draw keyframes
            ctx.fillStyle = 'red';
            for (let i = 0; i < times.length; i++) {
                const x = points[i * 2];
                const y = points[i * 2 + 1];
                ctx.fillRect(x - 4, y - 4, 8, 8);
                ctx.fillText(`${times[i]}`, x + 8, y - 8);
            }
            
            // Calculate and display speeds at keyframes
            let speedResults = `<h3>Speed Analysis (Smoothness: ${smoothness})</h3>`;
            speedResults += `<p>Expected: When smoothness=1, speed should be roughly uniform</p>`;
            
            const speeds = [];
            for (let i = 0; i < times.length; i++) {
                const speed = calculateSpeed(spline, times[i]);
                speeds.push(speed);
                speedResults += `<p>Frame ${times[i]}: Speed = ${speed.toFixed(2)} units/frame</p>`;
            }
            
            const avgSpeed = speeds.reduce((a, b) => a + b, 0) / speeds.length;
            const speedVariance = speeds.reduce((sum, speed) => sum + Math.pow(speed - avgSpeed, 2), 0) / speeds.length;
            const speedStdDev = Math.sqrt(speedVariance);
            
            speedResults += `<p><strong>Average Speed: ${avgSpeed.toFixed(2)}</strong></p>`;
            speedResults += `<p><strong>Speed Standard Deviation: ${speedStdDev.toFixed(2)}</strong></p>`;
            speedResults += `<p><strong>Speed Uniformity: ${speedStdDev < avgSpeed * 0.1 ? 'GOOD' : 'POOR'}</strong></p>`;
            
            document.getElementById('speed-results').innerHTML = speedResults;
        }

        // Setup controls
        const smoothnessSlider = document.getElementById('smoothness');
        const smoothnessValue = document.getElementById('smoothness-value');
        
        smoothnessSlider.addEventListener('input', () => {
            const value = parseFloat(smoothnessSlider.value);
            smoothnessValue.textContent = value;
            testSmoothness(value);
        });

        // Initial test
        testSmoothness(1);
    </script>
</body>
</html>
